<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.splitexpenses"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- Permissions for file operations -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />

    <!-- For Android 13+ (API 33+), we use the new granular media permissions -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- Network permissions for connectivity monitoring -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />

    <permission
        android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.example.splitexpenses.SplitExpensesApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SplitExpenses" >
        <activity
            android:name="com.example.splitexpenses.ui.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.SplitExpenses" >

            <!-- Main launcher intent filter -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Deep link intent filter for group invitations (old format) -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- URI scheme for deep links (old format) -->
                <data
                    android:host="join"
                    android:pathPattern="/.*"
                    android:scheme="splitexpenses" />
            </intent-filter>

            <!-- Deep link intent filter for group invitations (new format) -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- URI scheme for deep links (new format) -->
                <data
                    android:host="splitexpenses.example.com"
                    android:pathPrefix="/join"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.example.splitexpenses.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" /> <!-- Activity used to block background content while benchmarks are running -->
        <activity
            android:name="androidx.benchmark.IsolationActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
        </activity>
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundDialogTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.splitexpenses.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.tracing.perfetto.TracingReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >

            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
            <intent-filter>
                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
        </receiver>
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>