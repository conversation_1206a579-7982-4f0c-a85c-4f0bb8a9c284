1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.splitexpenses"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions for file operations -->
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:7:9-35
15    <uses-permission
15-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:8:5-9:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:8:22-78
17        android:maxSdkVersion="29" />
17-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:9:9-35
18
19    <!-- For Android 13+ (API 33+), we use the new granular media permissions -->
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:12:5-76
20-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:12:22-73
21
22    <!-- Network permissions for connectivity monitoring -->
23    <uses-permission android:name="android.permission.INTERNET" />
23-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:15:5-67
23-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:15:22-64
24    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
24-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:16:5-79
24-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:16:22-76
25    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
25-->[androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:23:5-25:53
25-->[androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:24:9-61
26    <uses-permission android:name="android.permission.REORDER_TASKS" />
26-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:24:5-72
26-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:24:22-69
27
28    <permission
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:18:5-66:19
35        android:name="com.example.splitexpenses.SplitExpensesApplication"
35-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:19:9-49
36        android:allowBackup="true"
36-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:20:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:21:9-65
39        android:debuggable="true"
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:22:9-54
42        android:icon="@mipmap/ic_launcher"
42-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:23:9-43
43        android:label="@string/app_name"
43-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:24:9-41
44        android:networkSecurityConfig="@xml/network_security_config"
44-->[androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:42:18-78
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:25:9-54
46        android:supportsRtl="true"
46-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:26:9-35
47        android:theme="@style/Theme.SplitExpenses" >
47-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:27:9-51
48        <activity
48-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:29:9-65:20
49            android:name="com.example.splitexpenses.ui.MainActivity"
49-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:30:13-44
50            android:exported="true"
50-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:31:13-36
51            android:label="@string/app_name"
51-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:32:13-45
52            android:theme="@style/Theme.SplitExpenses" >
52-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:33:13-55
53
54            <!-- Main launcher intent filter -->
55            <intent-filter>
55-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:35:13-38:29
56                <action android:name="android.intent.action.MAIN" />
56-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:36:17-69
56-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:36:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
58-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
59            </intent-filter>
60
61            <!-- Deep link intent filter for group invitations (old format) -->
62            <intent-filter>
62-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:41:13-51:29
63                <action android:name="android.intent.action.VIEW" />
63-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:17-69
63-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:25-66
64
65                <category android:name="android.intent.category.DEFAULT" />
65-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:17-76
65-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:27-73
66                <category android:name="android.intent.category.BROWSABLE" />
66-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:17-78
66-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:27-75
67
68                <!-- URI scheme for deep links (old format) -->
69                <data
69-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:47:17-50:49
70                    android:host="join"
70-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:49:21-40
71                    android:pathPattern="/.*"
71-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:50:21-46
72                    android:scheme="splitexpenses" />
72-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:48:21-51
73            </intent-filter>
74
75            <!-- Deep link intent filter for group invitations (new format) -->
76            <intent-filter>
76-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:54:13-64:29
77                <action android:name="android.intent.action.VIEW" />
77-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:17-69
77-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:25-66
78
79                <category android:name="android.intent.category.DEFAULT" />
79-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:17-76
79-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:27-73
80                <category android:name="android.intent.category.BROWSABLE" />
80-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:17-78
80-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:27-75
81
82                <!-- URI scheme for deep links (new format) -->
83                <data
83-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:47:17-50:49
84                    android:host="splitexpenses.example.com"
84-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:49:21-40
85                    android:pathPrefix="/join"
85-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:63:21-47
86                    android:scheme="https" />
86-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:48:21-51
87            </intent-filter>
88        </activity>
89
90        <service
90-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
91            android:name="com.google.firebase.components.ComponentDiscoveryService"
91-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:27:13-84
92            android:directBootAware="true"
92-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
93            android:exported="false" >
93-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:28:13-37
94            <meta-data
94-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
95                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
97            <meta-data
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
98                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
98-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
100            <meta-data
100-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
101                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
101-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
103            <meta-data
103-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
104                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
104-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
106        </service>
107
108        <activity
108-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
109            android:name="com.google.android.gms.common.api.GoogleApiActivity"
109-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
110            android:exported="false"
110-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
111            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
111-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
112
113        <provider
113-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
114            android:name="com.google.firebase.provider.FirebaseInitProvider"
114-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
115            android:authorities="com.example.splitexpenses.firebaseinitprovider"
115-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
116            android:directBootAware="true"
116-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
117            android:exported="false"
117-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
118            android:initOrder="100" />
118-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
119
120        <service
120-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
121            android:name="androidx.room.MultiInstanceInvalidationService"
121-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
122            android:directBootAware="true"
122-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
123            android:exported="false" /> <!-- Activity used to block background content while benchmarks are running -->
123-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
124        <activity
124-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:32:9-36:20
125            android:name="androidx.benchmark.IsolationActivity"
125-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:33:13-64
126            android:exported="true"
126-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:34:13-36
127            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
127-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:35:13-77
128        </activity>
129        <activity
129-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
130            android:name="androidx.activity.ComponentActivity"
130-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
131            android:exported="true" />
131-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
132        <activity
132-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
133            android:name="androidx.compose.ui.tooling.PreviewActivity"
133-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
134            android:exported="true" />
134-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
135        <activity
135-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:27:9-34:20
136            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
136-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:28:13-99
137            android:exported="true"
137-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:29:13-36
138            android:theme="@style/WhiteBackgroundTheme" >
138-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:30:13-56
139            <intent-filter android:priority="-100" >
139-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
139-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
140                <category android:name="android.intent.category.LAUNCHER" />
140-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
140-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
141            </intent-filter>
142        </activity>
143        <activity
143-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:35:9-42:20
144            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
144-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:36:13-95
145            android:exported="true"
145-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:37:13-36
146            android:theme="@style/WhiteBackgroundTheme" >
146-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:38:13-56
147            <intent-filter android:priority="-100" >
147-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
147-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
148                <category android:name="android.intent.category.LAUNCHER" />
148-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
148-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
149            </intent-filter>
150        </activity>
151        <activity
151-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:43:9-50:20
152            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
152-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:44:13-103
153            android:exported="true"
153-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:45:13-36
154            android:theme="@style/WhiteBackgroundDialogTheme" >
154-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:46:13-62
155            <intent-filter android:priority="-100" >
155-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
155-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
156                <category android:name="android.intent.category.LAUNCHER" />
156-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
156-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
157            </intent-filter>
158        </activity>
159
160        <provider
160-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
161            android:name="androidx.startup.InitializationProvider"
161-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:25:13-67
162            android:authorities="com.example.splitexpenses.androidx-startup"
162-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:26:13-68
163            android:exported="false" >
163-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:27:13-37
164            <meta-data
164-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
165                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
165-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
166                android:value="androidx.startup" />
166-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
167            <meta-data
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.emoji2.text.EmojiCompatInitializer"
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
169                android:value="androidx.startup" />
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
170            <meta-data
170-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
171                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
171-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
172                android:value="androidx.startup" />
172-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
173            <meta-data
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
175                android:value="androidx.startup" />
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
176        </provider>
177
178        <meta-data
178-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
179            android:name="com.google.android.gms.version"
179-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
180            android:value="@integer/google_play_services_version" />
180-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
181
182        <receiver
182-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
183            android:name="androidx.tracing.perfetto.TracingReceiver"
183-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
184            android:directBootAware="false"
184-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
185            android:enabled="true"
185-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
186            android:exported="true"
186-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
187            android:permission="android.permission.DUMP" >
187-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
188
189            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
190            <intent-filter>
190-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
191                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
191-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
191-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
192                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
192-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
192-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
193                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
193-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
193-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
194            </intent-filter>
195        </receiver>
196        <receiver
196-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
197            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
197-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
198            android:directBootAware="false"
198-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
199            android:enabled="false"
199-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
200            android:exported="false" >
200-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
201        </receiver>
202        <receiver
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
203            android:name="androidx.profileinstaller.ProfileInstallReceiver"
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
204            android:directBootAware="false"
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
205            android:enabled="true"
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
206            android:exported="true"
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
207            android:permission="android.permission.DUMP" >
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
209                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
212                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
215                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
218                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
219            </intent-filter>
220        </receiver>
221    </application>
222
223</manifest>
